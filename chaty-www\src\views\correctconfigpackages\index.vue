<template>
  <div class="main-wrapper">
    <el-page-header class="header-bar" @back="goBack">
      <template #content>
        <div style="display: flex;align-items: center">
          <el-image src="/icon/16.png" class="left-icon"></el-image>
          <el-text class="title">配标准卷</el-text>
          <el-input class="filter-item" v-model="correctConfigPackagesIndexPageData.filter.name" placeholder="请输入名称" />
          <el-button type="primary" @click="loadData" >搜索</el-button>
          <el-button linked @click="reset" style="margin-left: 10px">重置</el-button>
          <el-button linked @click="manualRefresh" style="margin-left: 10px; display: flex; align-items: center;">
            <el-icon style="margin-right: 2px;">
              <RefreshRight/>
            </el-icon>
            刷新
          </el-button>
          <div class="right" />
        </div>
      </template>
    </el-page-header>
    <div class="start-config">
      <div class="start-button" type="primary" icon="EditPen" @click="onEdit" style="background: #dff5ea">
        <el-image src="/icon/configStart.svg" class="icon"></el-image>
        <el-text class="text">配标准卷</el-text>
      </div>
    </div>
    <div class="main-content">
      <el-table v-loading="loading" :data="data" style="height: 100%" empty-text="无数据" :border="false">
        <el-table-column v-for="column in columns" :key="column.prop" v-bind="column"
                         :align="column?.align ?? 'center'"
                         border :fixed="column.prop === 'operations'? 'right':''">
          <template v-if="column.prop === 'operations'" v-slot="scope">
            <el-space :size="5" style="display: flex;flex-wrap: wrap;row-gap: 10px;justify-content: center">
              <el-button type="primary" text size="small" @click="onLook(scope.row.id)">查看</el-button>
              <el-button type="primary" text size="small" @click="editRemark(scope.row.id, scope.row.remark)">备注</el-button>
              <el-dropdown>
                    <span>
                      <el-icon class="el-icon--right">
                        <more/>
                      </el-icon>
                    </span>
                <template #dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item @click="onEditName(scope.row.id, scope.row.name)" >编辑名称</el-dropdown-item>
                    <el-dropdown-item @click="onDelete(scope.row.id, JSON.parse(scope.row.config ?? '{}'))" divided>删除</el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </el-space>
          </template>
          <template v-else-if="column.prop === 'type'" v-slot="scope">
            <el-icon>
              <EditPen/>
            </el-icon>
          </template>
          <template v-else-if="column.prop === 'config'" v-slot="scope">
              {{ JSON.parse(scope.row?.config ?? "[]").length }}页
          </template>
          <template v-else-if="column.prop === 'name'" v-slot="scope">
            <el-link type="primary" :underline="false" @click="onLook(scope.row.id)">{{ scope.row.name }}</el-link>
          </template>
          <template v-else-if="column.prop === 'updateTime'" v-slot="scope">
            {{
              $getFeiShuTimeFormat(scope.row?.updateTime)
            }}
          </template>
        </el-table-column>
      </el-table>
    </div>
    <div class="footer-bar">
      <el-pagination background layout="prev, pager, next" v-model:page-size="correctConfigPackagesIndexPageData.pageSize"
                     v-model:current-page="correctConfigPackagesIndexPageData.pageNumber" :total="total" @current-change="loadData" />
    </div>

    <!-- 文件上传表单 -->
    <right-drawer ref="fileForm" @onClose="loadData" />
  </div>
</template>
<script>
import RightDrawer from './components/rightDrawer.vue'
import {RefreshRight} from "@element-plus/icons-vue";
import {useUserStore} from "@/store";

const store = useUserStore();
export default {
  components: {
    RightDrawer,
    RefreshRight
  },
  data() {
    return {
      correctConfigPackagesIndexPageData: {
        filter: {
          name: "",
        },
        pageSize: 10,
        pageNumber: 1,
      },
      loading: false,
      data: [],
      columns: [
        {
          prop: "type",
          label: "类型",
          width: 100
        },
        {
          prop: "name",
          label: "试卷名称",
          align: "left",
        },
        {
          prop: "config",
          label: "配置页数",
          width: 125
        },
        {
          prop: "remark",
          label: "备注",
          width: 300
        },
        {
          prop: "updateTime",
          label: "更新时间",
          width: 200
        },
        {
          prop: "operations",
          label: "操作",
          width: 200
        },
      ],

      total: 0,
      timer: null,
    }
  },
  watch: {
    correctConfigPackagesIndexPageData: {
      handler(newVal) {
        store.setCorrectConfigPackagesIndexPageData(newVal);
      },
      deep: true
    }
  },
  created() {
    const correctConfigPackagesIndexPageData = store.getCorrectConfigPackagesIndexPageData;
    if (correctConfigPackagesIndexPageData) {
      this.correctConfigPackagesIndexPageData = JSON.parse(JSON.stringify(correctConfigPackagesIndexPageData));
    }
    this.loadData()
    this.timer = setInterval(() => {
      this.loadData()
    }, 10 * 1000);
  },
  beforeUnmount() {
    if (this.timer) {
      clearInterval(this.timer)
      this.timer = null
    }
  },
  methods: {
    reset() {
      this.correctConfigPackagesIndexPageData = {
        filter: {
          name: "",
        },
        pageSize: 10,
        pageNumber: 1,
      };
      this.loadData();
    },
    editRemark(id, remark) {
      this.$prompt('请输入新的备注', '修改备注', {
        inputValue: remark ?? '',
        confirmButtonText: '修改',
        cancelButtonText: '取消',
      }).then(({value}) => {
        const param = {
          id: id,
          remark: value
        }
        this.$axios.post("/api/docCorrectConfigPackage/update", param).then(res => {
          // 更新
          this.$message.success("修改成功！")
          this.loadData();
        })
      }).catch((e) => {
        // 用户点击取消时的处理逻辑
        this.$message.info("已取消修改");
      });
    },
    goBack() {
      this.$router.back();
    },
    /**
     * 点击"编辑名称"按钮时调用，弹出输入框修改试卷名称，且在提交前做名称重复检查
     * @param {number} id - 当前行的试卷 ID
     * @param {string} currentName - 当前试卷的名称，用于在输入框中显示初始值
     */
    onEditName(id, currentName) {
      // 使用 Element Plus 的 $prompt 弹出对话框
      this.$prompt('请输入新的试卷名称', '编辑名称', {
        inputValue: currentName,            // 初始值为当前名称
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        inputPlaceholder: '新的试卷名称',
        inputValidator: (value) => {
          if (!value || !value.trim()) {
            return '名称不能为空';
          }
          return true;
        },
        inputErrorMessage: '名称不能为空',
      })
          .then(({ value }) => {
            const newName = value.trim();
            // 在提交更新之前，先调用后端接口检查名称是否重复
            this.$axios
                .get(`/api/docCorrectConfigPackage/checkName?name=${encodeURIComponent(newName)}`)
                .then((res) => {
                  // 假设后端返回 res.data.exists 表示该名称是否已存在
                  // 如果接口返回字段不同，请根据实际情况调整下面的判断逻辑
                  if (res.data?.exists) {
                    this.$message.error('试卷名称已存在，请使用其他名称');
                    return;
                  }
                  // 名称不重复，继续调用更新接口
                  const payload = {
                    id: id,
                    name: newName,
                  };
                  this.$axios
                      .post('/api/docCorrectConfigPackage/update', payload)
                      .then(() => {
                        this.$message.success('修改成功！');
                        this.loadData();
                      })
                      .catch((err) => {
                        console.error(err);
                        this.$message.error('修改失败，请稍后重试');
                      });
                })
                .catch((err) => {
                  console.error(err);
                  this.$message.error('检查名称时发生错误，请稍后重试');
                });
          })
          .catch(() => {
            // 用户点击"取消"或关闭弹窗
            this.$message.info('已取消修改名称');
          });
    },
    onDelete(id, configIds) {
      this.$confirm("确认删除该试卷配置吗？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      }).then(() => {
        this.$axios.post(`/api/docCorrectConfigPackage/delete?id=${id}`).then(() => {
          this.$message.success("删除成功")
          this.loadData()
        });
        for(let i = 0; i < configIds.length; i++) {
          this.$axios.post(`/api/docCorrectConfig/delete?id=${configIds[i]}`).then(() => {
            this.loadData()
          });
        }
      })
    },
    loadData() {
      this.loading = true
      return this.$axios.post("/api/docCorrectConfigPackage/page", {
        page: {
          pageNumber: this.correctConfigPackagesIndexPageData.pageNumber,
          pageSize: this.correctConfigPackagesIndexPageData.pageSize
        },
        name: this.correctConfigPackagesIndexPageData.filter.name,
      }).then(res => {
        this.data = res.data.records
        this.total = res.data.total
      }).finally(() => {
        this.loading = false
      })
    },
    onEdit() {
      this.$router.push({ path: `/correctConfigPackages/upload` })
    },
    onLook(id) {
      this.$router.push({ path: `/correctConfigPackages/stepConfigPapers/${id}` })
    },
    async manualRefresh() {
      try {
        await this.loadData();
        this.$message({
          message: "加载成功",
          type: "success",
          duration: 1500,
        });
      } catch (e) {
        this.$message.error("刷新失败");
      }
    }
  }
}
</script>
<style lang="scss" scoped>

.main-wrapper {
  height: 100%;
  display: flex;
  flex-direction: column;
  .header-bar {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
    height: 48px;
    .left-icon {
      width: 28.28px;
      height: 22.89px;
      transform: scaleX(-1);
    }
    .title {
      font-weight: bold;
      font-size: 18px;
      color: #333333;
      letter-spacing: 0;
      margin-right: 19px;
    }
    .filter-item {
      margin-right: 10px;
      width: 200px;
    }

    .right {
      margin-left: auto !important;
    }

    .header-action {
      margin-right: 10px;

      :deep(.el-upload) {
        display: inline-block;
      }

      &.header-stats {
        width: 60px;
        text-align: center;
      }
    }

    .el-button+.el-button {
      margin-left: 0;
    }
  }

  .start-config {
    height: 135px;
    width: 100%;
    border-top: 2px solid #f5f5f5;
    border-bottom: 2px solid #f5f5f5;
    padding: 16px 0;
    display: flex;
    align-items: center;
    gap: 20px;

    .start-button {
      width: 253px;
      height: 103px;
      background: #dff5ea;
      border-radius: 7px;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      transition: all 0.3s ease;

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        border: 2px solid rgba(0, 0, 0, 0.2);
        background: #c9f2d6 !important; /* 配标准卷 - 绿色加深 */

        .text {
          font-weight: 700;
        }

        .icon {
          transform: scale(1.1);
        }
      }

      .icon {
        width: 55.89px;
        height: 57px;
        transition: transform 0.3s ease;
      }
      .text {
        font-weight: 600;
        font-size: 16px;
        color: #333333;
        letter-spacing: 0;
        text-align: center;
        margin-top: 4.5px;
        transition: font-weight 0.3s ease;
      }
    }
  }
  .main-content {
    flex: 1;
  }

  .footer-bar {
    height: 80px;
    display: flex;
    align-items: center;
    justify-content: flex-end;
  }
}
</style>